<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <?php if (isset($component)) { $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-header','data' => ['title' => 'Academic Terms Management','description' => 'Manage academic terms and semesters','backRoute' => route('admin.academic-calendar.index'),'backLabel' => 'Back to Academic Calendar']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Academic Terms Management','description' => 'Manage academic terms and semesters','back-route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.academic-calendar.index')),'back-label' => 'Back to Academic Calendar']); ?>
        <a href="<?php echo e(route('admin.academic-terms.create')); ?>" class="btn-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Academic Term
        </a>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $attributes = $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $component = $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>

    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg p-6" x-data="{
        searchQuery: '',
        academicYear: '',
        status: '',
        showAdvanced: false
    }">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
            <!-- Search Input -->
            <div class="flex-1">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input
                        type="text"
                        x-model="searchQuery"
                        @input="filterTerms()"
                        class="search-input"
                        placeholder="Search academic terms by name, year..."
                    >
                </div>
            </div>

            <!-- Filter Toggle and Clear Buttons -->
            <div class="flex items-center space-x-3">
                <button
                    @click="showAdvanced = !showAdvanced"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                    </svg>
                    Filters
                    <svg class="w-4 h-4 ml-1 transition-transform duration-200" :class="showAdvanced ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>

                <!-- Clear All Filters -->
                <button
                    @click="clearTermFilters()"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Clear All Filters
                </button>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div x-show="showAdvanced" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform -translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0" x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 transform translate-y-0" x-transition:leave-end="opacity-0 transform -translate-y-2" class="mt-4 pt-4 border-t border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Academic Year Filter -->
                <div>
                    <label for="year-filter" class="block text-sm font-medium text-gray-700 mb-1">Academic Year</label>
                    <select
                        id="year-filter"
                        x-model="academicYear"
                        @change="filterTerms()"
                        class="form-select"
                    >
                        <option value="">All Academic Years</option>
                        <?php $__currentLoopData = $academicYears; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $year): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($year->id); ?>"><?php echo e($year->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select
                        id="status-filter"
                        x-model="status"
                        @change="filterTerms()"
                        class="form-select"
                    >
                        <option value="">All Status</option>
                        <option value="current">Current</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Terms</dt>
                            <dd class="stat-card-value"><?php echo e($stats['total']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Current</dt>
                            <dd class="stat-card-value"><?php echo e($stats['current']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-yellow-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Active</dt>
                            <dd class="stat-card-value"><?php echo e($stats['active']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-purple-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Academic Years</dt>
                            <dd class="stat-card-value"><?php echo e($academicYears->count()); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Academic Terms List -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Academic Terms</h3>
        </div>
        <div class="p-6">
            <?php if($academicTerms->count() > 0): ?>
                <div class="space-y-4">
                    <?php $__currentLoopData = $academicTerms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $term): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200"
                             data-filterable
                             data-search-text="<?php echo e($term->name); ?> <?php echo e($term->academicYear->name); ?> <?php echo e($term->is_current ? 'current' : ''); ?> <?php echo e($term->is_active ? 'active' : 'inactive'); ?>"
                             data-academic-year="<?php echo e($term->academic_year_id); ?>"
                             data-status="<?php echo e($term->is_current ? 'current' : ($term->is_active ? 'active' : 'inactive')); ?>">

                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3">
                                        <h4 class="text-lg font-medium text-gray-900"><?php echo e($term->name); ?></h4>
                                        <?php if($term->is_current): ?>
                                            <span class="badge badge-green">Current</span>
                                        <?php endif; ?>
                                        <span class="badge <?php echo e($term->is_active ? 'badge-blue' : 'badge-gray'); ?>">
                                            <?php echo e($term->is_active ? 'Active' : 'Inactive'); ?>

                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1"><?php echo e($term->academicYear->name); ?></p>
                                    <div class="mt-3 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                                        <div>
                                            <span class="font-medium">Start Date:</span> <?php echo e($term->start_date->format('M d, Y')); ?>

                                        </div>
                                        <div>
                                            <span class="font-medium">End Date:</span> <?php echo e($term->end_date->format('M d, Y')); ?>

                                        </div>
                                        <div>
                                            <span class="font-medium">Duration:</span> <?php echo e($term->start_date->diffInDays($term->end_date)); ?> days
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <a href="<?php echo e(route('admin.academic-terms.show', $term)); ?>"
                                       class="inline-flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-md transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        View
                                    </a>
                                    <a href="<?php echo e(route('admin.academic-terms.edit', $term)); ?>"
                                       class="inline-flex items-center px-3 py-1.5 bg-indigo-600 hover:bg-indigo-700 text-white text-xs font-medium rounded-md transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        Edit
                                    </a>
                                    <?php if(!$term->is_current): ?>
                                        <form method="POST" action="<?php echo e(route('admin.academic-terms.set-current', $term)); ?>" class="inline">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit"
                                                    class="inline-flex items-center px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-md transition-colors duration-200">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                Set Current
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No academic terms</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by creating your first academic term.</p>
                    <div class="mt-6">
                        <a href="<?php echo e(route('admin.academic-terms.create')); ?>" class="btn-primary">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Academic Term
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Academic Terms List -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Academic Terms</h3>
                <span class="text-sm text-gray-500">
                    <span data-results-count><?php echo e($stats['total']); ?></span> terms found
                </span>
            </div>
        </div>
        <div class="p-6">
            <?php if($academicTerms->count() > 0): ?>
                <div class="space-y-4">
                    <?php $__currentLoopData = $academicTerms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $term): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200"
                             data-filterable
                             data-search-text="<?php echo e($term->name); ?> <?php echo e($term->academicYear->name); ?> <?php echo e($term->is_current ? 'current' : ''); ?> <?php echo e($term->is_active ? 'active' : 'inactive'); ?>"
                             data-academic-year="<?php echo e($term->academic_year_id); ?>"
                             data-status="<?php echo e($term->is_current ? 'current' : ($term->is_active ? 'active' : 'inactive')); ?>">

                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3">
                                        <h4 class="text-lg font-medium text-gray-900"><?php echo e($term->name); ?></h4>
                                        <?php if($term->is_current): ?>
                                            <span class="badge badge-green">Current</span>
                                        <?php endif; ?>
                                        <span class="badge <?php echo e($term->is_active ? 'badge-blue' : 'badge-gray'); ?>">
                                            <?php echo e($term->is_active ? 'Active' : 'Inactive'); ?>

                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1"><?php echo e($term->academicYear->name); ?></p>
                                    <div class="mt-3 grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600">
                                        <div>
                                            <span class="font-medium">Start Date:</span>
                                            <br><?php echo e($term->start_date->format('M d, Y')); ?>

                                        </div>
                                        <div>
                                            <span class="font-medium">End Date:</span>
                                            <br><?php echo e($term->end_date->format('M d, Y')); ?>

                                        </div>
                                        <div>
                                            <span class="font-medium">Duration:</span>
                                            <br><?php echo e($term->start_date->diffInDays($term->end_date)); ?> days
                                        </div>
                                        <div>
                                            <span class="font-medium">Sort Order:</span>
                                            <br><?php echo e($term->sort_order); ?>

                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <a href="<?php echo e(route('admin.academic-terms.show', $term)); ?>"
                                       class="inline-flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-md transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        View
                                    </a>
                                    <a href="<?php echo e(route('admin.academic-terms.edit', $term)); ?>"
                                       class="inline-flex items-center px-3 py-1.5 bg-indigo-600 hover:bg-indigo-700 text-white text-xs font-medium rounded-md transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        Edit
                                    </a>
                                    <?php if(!$term->is_current): ?>
                                        <form method="POST" action="<?php echo e(route('admin.academic-terms.set-current', $term)); ?>" class="inline">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit"
                                                    class="inline-flex items-center px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-md transition-colors duration-200">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                Set Current
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No academic terms</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by creating your first academic term.</p>
                    <div class="mt-6">
                        <a href="<?php echo e(route('admin.academic-terms.create')); ?>" class="btn-primary">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Academic Term
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Live filtering functions for academic terms
function filterTerms() {
    const searchQuery = document.querySelector('[x-model="searchQuery"]').value.toLowerCase();
    const academicYearFilter = document.querySelector('[x-model="academicYear"]').value;
    const statusFilter = document.querySelector('[x-model="status"]').value;

    const terms = document.querySelectorAll('[data-filterable]');
    let visibleCount = 0;

    terms.forEach(term => {
        const searchText = term.getAttribute('data-search-text').toLowerCase();
        const termAcademicYear = term.getAttribute('data-academic-year');
        const termStatus = term.getAttribute('data-status');

        let isVisible = true;

        // Search filter
        if (searchQuery && !searchText.includes(searchQuery)) {
            isVisible = false;
        }

        // Academic year filter
        if (academicYearFilter && termAcademicYear !== academicYearFilter) {
            isVisible = false;
        }

        // Status filter
        if (statusFilter && termStatus !== statusFilter) {
            isVisible = false;
        }

        // Show/hide term
        term.style.display = isVisible ? '' : 'none';
        if (isVisible) visibleCount++;
    });

    // Update results count
    const resultsCount = document.querySelector('[data-results-count]');
    if (resultsCount) {
        resultsCount.textContent = visibleCount;
    }
}

// Clear all filters
function clearTermFilters() {
    // Reset form inputs and trigger events
    const searchInput = document.querySelector('[x-model="searchQuery"]');
    const academicYearInput = document.querySelector('[x-model="academicYear"]');
    const statusInput = document.querySelector('[x-model="status"]');

    if (searchInput) {
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input'));
    }
    if (academicYearInput) {
        academicYearInput.value = '';
        academicYearInput.dispatchEvent(new Event('change'));
    }
    if (statusInput) {
        statusInput.value = '';
        statusInput.dispatchEvent(new Event('change'));
    }

    // Show all terms
    const terms = document.querySelectorAll('[data-filterable]');
    terms.forEach(term => {
        term.style.display = '';
    });

    // Update results count
    const resultsCount = document.querySelector('[data-results-count]');
    if (resultsCount) {
        resultsCount.textContent = terms.length;
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Live filtering functions for academic terms
function filterTerms() {
    const searchQuery = document.querySelector('[x-model="searchQuery"]').value.toLowerCase();
    const academicYearFilter = document.querySelector('[x-model="academicYear"]').value;
    const statusFilter = document.querySelector('[x-model="status"]').value;

    // Get all filterable items
    const items = document.querySelectorAll('[data-filterable]');

    items.forEach(item => {
        let showItem = true;

        // Text search
        if (searchQuery) {
            const searchableText = item.getAttribute('data-search-text') || item.textContent;
            if (!searchableText.toLowerCase().includes(searchQuery)) {
                showItem = false;
            }
        }

        // Academic Year filtering
        if (academicYearFilter) {
            const itemYear = item.getAttribute('data-academic-year');
            if (itemYear !== academicYearFilter) {
                showItem = false;
            }
        }

        // Status filtering
        if (statusFilter) {
            const itemStatus = item.getAttribute('data-status');
            if (itemStatus !== statusFilter) {
                showItem = false;
            }
        }

        // Show/hide item
        item.style.display = showItem ? '' : 'none';
    });
}

function clearTermFilters() {
    // Reset form inputs and trigger events
    const searchInput = document.querySelector('[x-model="searchQuery"]');
    const yearInput = document.querySelector('[x-model="academicYear"]');
    const statusInput = document.querySelector('[x-model="status"]');

    if (searchInput) {
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input'));
    }
    if (yearInput) {
        yearInput.value = '';
        yearInput.dispatchEvent(new Event('change'));
    }
    if (statusInput) {
        statusInput.value = '';
        statusInput.dispatchEvent(new Event('change'));
    }

    // Show all items
    const items = document.querySelectorAll('[data-filterable]');
    items.forEach(item => {
        item.style.display = '';
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\school-management-system\resources\views/admin/academic-terms/index.blade.php ENDPATH**/ ?>