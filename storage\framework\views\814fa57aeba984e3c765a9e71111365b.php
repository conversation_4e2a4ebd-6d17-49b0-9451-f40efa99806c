<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <?php if (isset($component)) { $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-header','data' => ['title' => 'Time Slot Details','description' => 'View time slot information and related schedules','backRoute' => route('admin.time-slots.index'),'backLabel' => 'Back to Time Slots']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Time Slot Details','description' => 'View time slot information and related schedules','back-route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.time-slots.index')),'back-label' => 'Back to Time Slots']); ?>
        
        <div class="flex items-center space-x-3">
            <a href="<?php echo e(route('admin.time-slots.edit', $timeSlot)); ?>" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Time Slot
            </a>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $attributes = $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $component = $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>

    <!-- Time Slot Details -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gradient-to-r from-blue-500 to-blue-600">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-6">
                    <h1 class="text-2xl font-bold text-white"><?php echo e($timeSlot->name); ?></h1>
                    <p class="text-blue-100">
                        <?php echo e(\Carbon\Carbon::parse($timeSlot->start_time)->format('g:i A')); ?> - 
                        <?php echo e(\Carbon\Carbon::parse($timeSlot->end_time)->format('g:i A')); ?>

                    </p>
                    <div class="flex items-center mt-2 space-x-4">
                        <?php
                            $typeBadgeClasses = [
                                'class' => 'badge-blue',
                                'break' => 'badge-yellow',
                                'lunch' => 'badge-orange',
                                'assembly' => 'badge-purple',
                            ];
                        ?>
                        <span class="badge <?php echo e($typeBadgeClasses[$timeSlot->type] ?? 'badge-gray'); ?>">
                            <?php echo e(ucfirst($timeSlot->type)); ?>

                        </span>
                        <span class="badge <?php echo e($timeSlot->is_active ? 'badge-green' : 'badge-red'); ?>">
                            <?php echo e($timeSlot->is_active ? 'Active' : 'Inactive'); ?>

                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="px-6 py-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Basic Information -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Name</dt>
                            <dd class="text-sm text-gray-900"><?php echo e($timeSlot->name); ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Start Time</dt>
                            <dd class="text-sm text-gray-900"><?php echo e(\Carbon\Carbon::parse($timeSlot->start_time)->format('g:i A')); ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">End Time</dt>
                            <dd class="text-sm text-gray-900"><?php echo e(\Carbon\Carbon::parse($timeSlot->end_time)->format('g:i A')); ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Duration</dt>
                            <dd class="text-sm text-gray-900">
                                <?php echo e(\Carbon\Carbon::parse($timeSlot->start_time)->diffInMinutes(\Carbon\Carbon::parse($timeSlot->end_time))); ?> minutes
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Type</dt>
                            <dd class="text-sm text-gray-900"><?php echo e(ucfirst($timeSlot->type)); ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Sort Order</dt>
                            <dd class="text-sm text-gray-900"><?php echo e($timeSlot->sort_order); ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Status</dt>
                            <dd class="text-sm text-gray-900"><?php echo e($timeSlot->is_active ? 'Active' : 'Inactive'); ?></dd>
                        </div>
                    </dl>
                </div>

                <!-- Usage Statistics -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Usage Statistics</h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Total Schedules</dt>
                            <dd class="text-sm text-gray-900"><?php echo e($timeSlot->schedules->count()); ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Active Schedules</dt>
                            <dd class="text-sm text-gray-900"><?php echo e($timeSlot->schedules->where('is_active', true)->count()); ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Classes Using</dt>
                            <dd class="text-sm text-gray-900"><?php echo e($timeSlot->schedules->pluck('class_id')->unique()->count()); ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Teachers Assigned</dt>
                            <dd class="text-sm text-gray-900"><?php echo e($timeSlot->schedules->pluck('teacher_id')->unique()->count()); ?></dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Schedules -->
    <?php if($timeSlot->schedules->isNotEmpty()): ?>
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Related Schedules</h3>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class & Section</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Day</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Room</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php $__currentLoopData = $timeSlot->schedules->where('is_active', true); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($schedule->class->name); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($schedule->section->name); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($schedule->subject->name); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($schedule->subject->code); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($schedule->teacher->name); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo e(ucfirst($schedule->day_of_week)); ?>

                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo e($schedule->room_number ?: 'Not assigned'); ?>

                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\school-management-system\resources\views/admin/time-slots/show.blade.php ENDPATH**/ ?>