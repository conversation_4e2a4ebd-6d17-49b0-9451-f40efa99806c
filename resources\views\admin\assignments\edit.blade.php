@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <x-page-header
        title="Edit Assignment"
        description="Update assignment information and settings"
        :back-route="route('admin.grading.assignments.show', $assignment)"
        back-label="Back to Assignment" />

    <!-- Edit Form -->
    <div class="card">
        <form method="POST" action="{{ route('admin.grading.assignments.update', $assignment) }}" class="space-y-6">
            @csrf
            @method('PUT')
            
            <div class="card-body space-y-6">
                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="title" class="form-label">Assignment Title</label>
                        <input type="text" 
                               id="title" 
                               name="title" 
                               value="{{ old('title', $assignment->title) }}" 
                               class="form-input @error('title') border-red-300 @enderror" 
                               required>
                        @error('title')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="assignment_code" class="form-label">Assignment Code</label>
                        <input type="text" 
                               id="assignment_code" 
                               name="assignment_code" 
                               value="{{ old('assignment_code', $assignment->assignment_code) }}" 
                               class="form-input @error('assignment_code') border-red-300 @enderror" 
                               required>
                        @error('assignment_code')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Academic Information -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="academic_year_id" class="form-label">Academic Year</label>
                        <select id="academic_year_id" 
                                name="academic_year_id" 
                                class="form-select @error('academic_year_id') border-red-300 @enderror" 
                                required>
                            <option value="">Select Academic Year</option>
                            @foreach($academicYears as $year)
                                <option value="{{ $year->id }}" {{ old('academic_year_id', $assignment->academic_year_id) == $year->id ? 'selected' : '' }}>
                                    {{ $year->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('academic_year_id')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="academic_term_id" class="form-label">Academic Term</label>
                        <select id="academic_term_id" 
                                name="academic_term_id" 
                                class="form-select @error('academic_term_id') border-red-300 @enderror" 
                                required>
                            <option value="">Select Academic Term</option>
                            @foreach($academicTerms as $term)
                                <option value="{{ $term->id }}" {{ old('academic_term_id', $assignment->academic_term_id) == $term->id ? 'selected' : '' }}>
                                    {{ $term->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('academic_term_id')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="grade_category_id" class="form-label">Grade Category</label>
                        <select id="grade_category_id" 
                                name="grade_category_id" 
                                class="form-select @error('grade_category_id') border-red-300 @enderror" 
                                required>
                            <option value="">Select Grade Category</option>
                            @foreach($gradeCategories as $category)
                                <option value="{{ $category->id }}" {{ old('grade_category_id', $assignment->grade_category_id) == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }} ({{ $category->weight_percentage }}%)
                                </option>
                            @endforeach
                        </select>
                        @error('grade_category_id')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Class and Subject -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="subject_id" class="form-label">Subject</label>
                        <select id="subject_id" 
                                name="subject_id" 
                                class="form-select @error('subject_id') border-red-300 @enderror" 
                                required>
                            <option value="">Select Subject</option>
                            @foreach($subjects as $subject)
                                <option value="{{ $subject->id }}" {{ old('subject_id', $assignment->subject_id) == $subject->id ? 'selected' : '' }}>
                                    {{ $subject->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('subject_id')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="class_id" class="form-label">Class</label>
                        <select id="class_id" 
                                name="class_id" 
                                class="form-select @error('class_id') border-red-300 @enderror" 
                                required>
                            <option value="">Select Class</option>
                            @foreach($classRooms as $class)
                                <option value="{{ $class->id }}" {{ old('class_id', $assignment->class_id) == $class->id ? 'selected' : '' }}>
                                    {{ $class->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('class_id')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="teacher_id" class="form-label">Teacher</label>
                        <select id="teacher_id" 
                                name="teacher_id" 
                                class="form-select @error('teacher_id') border-red-300 @enderror" 
                                required>
                            <option value="">Select Teacher</option>
                            @foreach($teachers as $teacher)
                                <option value="{{ $teacher->id }}" {{ old('teacher_id', $assignment->teacher_id) == $teacher->id ? 'selected' : '' }}>
                                    {{ $teacher->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('teacher_id')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Assignment Dates -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="assigned_date" class="form-label">Assigned Date</label>
                        <input type="date" 
                               id="assigned_date" 
                               name="assigned_date" 
                               value="{{ old('assigned_date', $assignment->assigned_date->format('Y-m-d')) }}" 
                               class="form-input @error('assigned_date') border-red-300 @enderror" 
                               required>
                        @error('assigned_date')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="due_date" class="form-label">Due Date</label>
                        <input type="date" 
                               id="due_date" 
                               name="due_date" 
                               value="{{ old('due_date', $assignment->due_date->format('Y-m-d')) }}" 
                               class="form-input @error('due_date') border-red-300 @enderror" 
                               required>
                        @error('due_date')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="due_time" class="form-label">Due Time (Optional)</label>
                        <input type="time" 
                               id="due_time" 
                               name="due_time" 
                               value="{{ old('due_time', $assignment->due_time ? $assignment->due_time->format('H:i') : '') }}" 
                               class="form-input @error('due_time') border-red-300 @enderror">
                        @error('due_time')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Grading Information -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="total_marks" class="form-label">Total Marks</label>
                        <input type="number" 
                               id="total_marks" 
                               name="total_marks" 
                               value="{{ old('total_marks', $assignment->total_marks) }}" 
                               class="form-input @error('total_marks') border-red-300 @enderror" 
                               min="1" 
                               step="0.01" 
                               required>
                        @error('total_marks')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="passing_marks" class="form-label">Passing Marks</label>
                        <input type="number" 
                               id="passing_marks" 
                               name="passing_marks" 
                               value="{{ old('passing_marks', $assignment->passing_marks) }}" 
                               class="form-input @error('passing_marks') border-red-300 @enderror" 
                               min="0" 
                               step="0.01">
                        @error('passing_marks')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="grade_scale_id" class="form-label">Grade Scale</label>
                        <select id="grade_scale_id" 
                                name="grade_scale_id" 
                                class="form-select @error('grade_scale_id') border-red-300 @enderror">
                            <option value="">Select Grade Scale</option>
                            @foreach($gradeScales as $scale)
                                <option value="{{ $scale->id }}" {{ old('grade_scale_id', $assignment->grade_scale_id) == $scale->id ? 'selected' : '' }}>
                                    {{ $scale->name }} ({{ ucfirst($scale->type) }})
                                </option>
                            @endforeach
                        </select>
                        @error('grade_scale_id')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Submission Settings -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="submission_type" class="form-label">Submission Type</label>
                        <select id="submission_type" 
                                name="submission_type" 
                                class="form-select @error('submission_type') border-red-300 @enderror" 
                                required>
                            <option value="online" {{ old('submission_type', $assignment->submission_type) === 'online' ? 'selected' : '' }}>Online</option>
                            <option value="offline" {{ old('submission_type', $assignment->submission_type) === 'offline' ? 'selected' : '' }}>Offline</option>
                            <option value="both" {{ old('submission_type', $assignment->submission_type) === 'both' ? 'selected' : '' }}>Both</option>
                        </select>
                        @error('submission_type')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="max_attempts" class="form-label">Max Attempts</label>
                        <input type="number" 
                               id="max_attempts" 
                               name="max_attempts" 
                               value="{{ old('max_attempts', $assignment->max_attempts) }}" 
                               class="form-input @error('max_attempts') border-red-300 @enderror" 
                               min="1">
                        @error('max_attempts')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="late_penalty_percentage" class="form-label">Late Penalty (%)</label>
                        <input type="number" 
                               id="late_penalty_percentage" 
                               name="late_penalty_percentage" 
                               value="{{ old('late_penalty_percentage', $assignment->late_penalty_percentage) }}" 
                               class="form-input @error('late_penalty_percentage') border-red-300 @enderror" 
                               min="0" 
                               max="100" 
                               step="0.01">
                        @error('late_penalty_percentage')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Status and Publishing -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="status" class="form-label">Status</label>
                        <select id="status" 
                                name="status" 
                                class="form-select @error('status') border-red-300 @enderror" 
                                required>
                            <option value="draft" {{ old('status', $assignment->status) === 'draft' ? 'selected' : '' }}>Draft</option>
                            <option value="active" {{ old('status', $assignment->status) === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="completed" {{ old('status', $assignment->status) === 'completed' ? 'selected' : '' }}>Completed</option>
                            <option value="cancelled" {{ old('status', $assignment->status) === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                        @error('status')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="space-y-3 pt-6">
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="is_published" 
                                   name="is_published" 
                                   value="1"
                                   {{ old('is_published', $assignment->is_published) ? 'checked' : '' }}
                                   class="form-checkbox">
                            <label for="is_published" class="ml-2 text-sm text-gray-700">Publish assignment (visible to students)</label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="allow_late_submission" 
                                   name="allow_late_submission" 
                                   value="1"
                                   {{ old('allow_late_submission', $assignment->allow_late_submission) ? 'checked' : '' }}
                                   class="form-checkbox">
                            <label for="allow_late_submission" class="ml-2 text-sm text-gray-700">Allow late submissions</label>
                        </div>
                    </div>
                </div>

                <!-- Description and Instructions -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="description" class="form-label">Description</label>
                        <textarea id="description" 
                                  name="description" 
                                  rows="4" 
                                  class="form-textarea @error('description') border-red-300 @enderror" 
                                  placeholder="Enter assignment description...">{{ old('description', $assignment->description) }}</textarea>
                        @error('description')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="instructions" class="form-label">Instructions</label>
                        <textarea id="instructions" 
                                  name="instructions" 
                                  rows="4" 
                                  class="form-textarea @error('instructions') border-red-300 @enderror" 
                                  placeholder="Enter detailed instructions for students...">{{ old('instructions', $assignment->instructions) }}</textarea>
                        @error('instructions')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="{{ route('admin.grading.assignments.show', $assignment) }}" class="btn-cancel" style="cursor: pointer;">
                        Cancel
                    </a>
                    <button type="submit" class="btn-primary" style="cursor: pointer;">
                        Update Assignment
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection
