<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Page Header -->
    <?php if (isset($component)) { $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-header','data' => ['title' => 'Assignment Details','description' => 'View assignment information and submissions','backRoute' => route('admin.grading.assignments.index'),'backLabel' => 'Back to Assignments']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Assignment Details','description' => 'View assignment information and submissions','back-route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.grading.assignments.index')),'back-label' => 'Back to Assignments']); ?>
        
        <div class="flex items-center space-x-3">
            <a href="<?php echo e(route('admin.grading.assignments.edit', $assignment)); ?>" class="btn-secondary" style="cursor: pointer;">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
                Edit Assignment
            </a>

            <form method="POST" action="<?php echo e(route('admin.grading.assignments.toggle-status', $assignment)); ?>" class="inline toggle-assignment-status-form">
                <?php echo csrf_field(); ?>
                <button type="button"
                        class="btn-<?php echo e($assignment->status === 'active' ? 'warning' : 'success'); ?> toggle-assignment-status-btn"
                        style="cursor: pointer;"
                        data-assignment-id="<?php echo e($assignment->id); ?>"
                        data-assignment-title="<?php echo e($assignment->title); ?>"
                        data-current-status="<?php echo e($assignment->status); ?>">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                    </svg>
                    <?php echo e($assignment->status === 'active' ? 'Set to Draft' : 'Activate'); ?>

                </button>
            </form>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $attributes = $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $component = $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>

    <!-- Assignment Information -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gradient-to-r from-blue-500 to-blue-600">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-white"><?php echo e($assignment->title); ?></h1>
                    <p class="text-blue-100"><?php echo e($assignment->assignment_code); ?></p>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="badge <?php echo e($assignment->status === 'active' ? 'badge-green' : 'badge-yellow'); ?>">
                        <?php echo e(ucfirst($assignment->status)); ?>

                    </span>
                    <?php if($assignment->is_published): ?>
                        <span class="badge badge-blue">Published</span>
                    <?php else: ?>
                        <span class="badge badge-gray">Draft</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Submission Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Students</dt>
                            <dd class="stat-card-value"><?php echo e($submissionStats['total_students']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Submitted</dt>
                            <dd class="stat-card-value"><?php echo e($submissionStats['submitted']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-yellow-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Pending</dt>
                            <dd class="stat-card-value"><?php echo e($submissionStats['pending']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-purple-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Graded</dt>
                            <dd class="stat-card-value"><?php echo e($submissionStats['graded']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Assignment Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Assignment Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Title</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($assignment->title); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Assignment Code</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($assignment->assignment_code); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Subject</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($assignment->subject->name); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Class</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($assignment->classRoom->name); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Teacher</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($assignment->teacher ? $assignment->teacher->name : 'Not assigned'); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Grade Category</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($assignment->gradeCategory->name); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Assigned Date</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($assignment->assigned_date->format('M d, Y')); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Due Date</label>
                        <p class="mt-1 text-sm text-gray-900">
                            <?php echo e($assignment->due_date->format('M d, Y')); ?>

                            <?php if($assignment->due_time): ?>
                                at <?php echo e($assignment->due_time->format('H:i')); ?>

                            <?php endif; ?>
                        </p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Total Marks</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e(number_format($assignment->total_marks, 1)); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Passing Marks</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e(number_format($assignment->passing_marks, 1)); ?></p>
                    </div>
                    
                    <?php if($assignment->description): ?>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Description</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($assignment->description); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($assignment->instructions): ?>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Instructions</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($assignment->instructions); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Submissions -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Recent Submissions</h3>
                        <a href="<?php echo e(route('admin.grading.assignments.submissions', $assignment)); ?>" class="text-sm text-blue-600 hover:text-blue-500" style="cursor: pointer;">
                            View all submissions
                        </a>
                    </div>
                </div>

                <?php if($assignment->submissions->count() > 0): ?>
                    <div class="divide-y divide-gray-200">
                        <?php $__currentLoopData = $assignment->submissions->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $submission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="px-6 py-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900"><?php echo e($submission->student->user->name); ?></p>
                                        <p class="text-sm text-gray-500">
                                            Submitted: <?php echo e($submission->submitted_at->format('M d, Y H:i')); ?>

                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <?php if($submission->grade && $submission->grade->marks_obtained !== null): ?>
                                            <p class="text-sm font-medium text-gray-900">
                                                <?php echo e(number_format($submission->grade->marks_obtained, 1)); ?>/<?php echo e(number_format($assignment->total_marks, 1)); ?>

                                            </p>
                                            <span class="badge badge-green">Graded</span>
                                        <?php else: ?>
                                            <span class="badge badge-yellow">Pending Review</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="px-6 py-8 text-center">
                        <p class="text-sm text-gray-500">No submissions yet.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Assignment Settings -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Assignment Settings</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Submission Type:</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e(ucfirst($assignment->submission_type)); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Late Submission:</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($assignment->allow_late_submission ? 'Allowed' : 'Not Allowed'); ?></span>
                    </div>
                    <?php if($assignment->allow_late_submission && $assignment->late_penalty_percentage): ?>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Late Penalty:</span>
                            <span class="text-sm font-medium text-gray-900"><?php echo e($assignment->late_penalty_percentage); ?>%</span>
                        </div>
                    <?php endif; ?>
                    <?php if($assignment->max_attempts): ?>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Max Attempts:</span>
                            <span class="text-sm font-medium text-gray-900"><?php echo e($assignment->max_attempts); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Performance Summary -->
            <?php if($submissionStats['graded'] > 0): ?>
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Performance Summary</h3>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Average Score:</span>
                            <span class="text-sm font-medium text-gray-900">
                                <?php echo e(number_format($submissionStats['average_marks'], 1)); ?>/<?php echo e(number_format($assignment->total_marks, 1)); ?>

                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Completion Rate:</span>
                            <span class="text-sm font-medium text-gray-900">
                                <?php echo e(number_format(($submissionStats['submitted'] / $submissionStats['total_students']) * 100, 1)); ?>%
                            </span>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    <a href="<?php echo e(route('admin.grading.assignments.edit', $assignment)); ?>"
                       class="w-full btn-primary text-center" style="cursor: pointer;">
                        Edit Assignment
                    </a>

                    <a href="<?php echo e(route('admin.grading.assignments.submissions', $assignment)); ?>"
                       class="w-full btn-secondary text-center" style="cursor: pointer;">
                        View Submissions
                    </a>

                    <form method="POST" action="<?php echo e(route('admin.grading.assignments.toggle-status', $assignment)); ?>" class="w-full toggle-assignment-status-form">
                        <?php echo csrf_field(); ?>
                        <button type="button"
                                class="w-full btn-<?php echo e($assignment->status === 'active' ? 'warning' : 'success'); ?> text-center toggle-assignment-status-btn"
                                style="cursor: pointer;"
                                data-assignment-id="<?php echo e($assignment->id); ?>"
                                data-assignment-title="<?php echo e($assignment->title); ?>"
                                data-current-status="<?php echo e($assignment->status); ?>">
                            <?php echo e($assignment->status === 'active' ? 'Set to Draft' : 'Activate'); ?>

                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Handle toggle assignment status with custom modal confirmation
document.addEventListener('DOMContentLoaded', function() {
    const toggleButtons = document.querySelectorAll('.toggle-assignment-status-btn');

    toggleButtons.forEach(button => {
        button.addEventListener('click', async function(e) {
            e.preventDefault();

            const assignmentId = this.getAttribute('data-assignment-id');
            const assignmentTitle = this.getAttribute('data-assignment-title');
            const currentStatus = this.getAttribute('data-current-status');

            const action = currentStatus === 'active' ? 'set to draft' : 'activate';
            const actionText = currentStatus === 'active' ? 'Set to Draft' : 'Activate';

            const confirmed = await confirmModal({
                title: `${actionText} Assignment`,
                message: `Are you sure you want to ${action} the assignment "${assignmentTitle}"?`,
                confirmText: actionText,
                cancelText: 'Cancel',
                type: currentStatus === 'active' ? 'warning' : 'success'
            });

            if (confirmed) {
                // Submit the form
                const form = this.closest('.toggle-assignment-status-form');
                form.submit();
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\school-management-system\resources\views/admin/assignments/show.blade.php ENDPATH**/ ?>