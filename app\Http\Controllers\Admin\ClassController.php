<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ClassModel;
use App\Models\Section;
use App\Models\Student;
use App\Models\StudentEnrollment;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ClassController extends Controller
{
    /**
     * Display a listing of classes
     */
    public function index(Request $request)
    {
        $query = ClassModel::withCount(['sections', 'students', 'subjects']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('level', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $classes = $query->ordered()->paginate(20);

        // Get filter options
        $levels = ClassModel::distinct()->pluck('level')->filter()->sort();

        // Statistics
        $stats = [
            'total_classes' => ClassModel::count(),
            'active_classes' => ClassModel::active()->count(),
            'total_sections' => Section::count(),
            'total_students' => \App\Models\Student::count(),
        ];

        return view('admin.classes.index', compact('classes', 'levels', 'stats'));
    }

    /**
     * Show the form for creating a new class
     */
    public function create()
    {
        return view('admin.classes.create');
    }

    /**
     * Store a newly created class
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:classes',
            'level' => 'required|string|max:100',
            'description' => 'nullable|string',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $class = ClassModel::create([
                'name' => $request->name,
                'level' => $request->level,
                'description' => $request->description,
                'sort_order' => $request->sort_order,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Log activity
            ActivityLog::log(
                'created_class',
                "Created class: {$class->name}",
                'App\Models\ClassModel',
                $class->id
            );

            DB::commit();

            return redirect()->route('admin.academic.classes.index')
                           ->with('success', 'Class created successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Error creating class: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified class
     */
    public function show(ClassModel $class)
    {
        $class->load(['sections.students', 'students', 'subjects', 'teachers']);
        
        return view('admin.classes.show', compact('class'));
    }

    /**
     * Show the form for editing the specified class
     */
    public function edit(ClassModel $class)
    {
        return view('admin.classes.edit', compact('class'));
    }

    /**
     * Update the specified class
     */
    public function update(Request $request, ClassModel $class)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:classes,name,' . $class->id,
            'level' => 'required|string|max:100',
            'description' => 'nullable|string',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $oldData = $class->toArray();

            $class->update([
                'name' => $request->name,
                'level' => $request->level,
                'description' => $request->description,
                'sort_order' => $request->sort_order,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Log activity
            ActivityLog::log(
                'updated_class',
                "Updated class: {$class->name}",
                'App\Models\ClassModel',
                $class->id,
                ['old_data' => $oldData, 'new_data' => $class->fresh()->toArray()]
            );

            DB::commit();

            return redirect()->route('admin.academic.classes.index')
                           ->with('success', 'Class updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Error updating class: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified class
     */
    public function destroy(ClassModel $class)
    {
        try {
            DB::beginTransaction();

            // Check if class has students or sections
            if ($class->students()->count() > 0 || $class->sections()->count() > 0) {
                return back()->with('error', 'Cannot delete class that has students or sections.');
            }

            $className = $class->name;

            $class->delete();

            // Log activity
            ActivityLog::log(
                'deleted_class',
                "Deleted class: {$className}",
                'App\Models\ClassModel',
                null
            );

            DB::commit();

            return redirect()->route('admin.academic.classes.index')
                           ->with('success', 'Class deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Error deleting class: ' . $e->getMessage());
        }
    }

    /**
     * Toggle class status
     */
    public function toggleStatus(ClassModel $class)
    {
        try {
            $class->update(['is_active' => !$class->is_active]);

            $status = $class->is_active ? 'activated' : 'deactivated';
            
            // Log activity
            ActivityLog::log(
                $status . '_class',
                "Class {$status}: {$class->name}",
                'App\Models\ClassModel',
                $class->id
            );

            return back()->with('success', "Class {$status} successfully.");

        } catch (\Exception $e) {
            return back()->with('error', 'Error updating class status: ' . $e->getMessage());
        }
    }

    /**
     * Get sections for a class (AJAX)
     */
    public function getSections($classId)
    {
        try {
            \Log::info('Fetching sections for class ID: ' . $classId);

            $class = ClassModel::findOrFail($classId);
            \Log::info('Found class: ' . $class->name);

            $sections = $class->sections()
                ->orderBy('name')
                ->get()
                ->map(function ($section) {
                    // Get students count manually to avoid relationship issues
                    $studentsCount = StudentEnrollment::where('section_id', $section->id)
                        ->where('is_active', true)
                        ->count();

                    return [
                        'id' => $section->id,
                        'name' => $section->name ?? 'Unknown Section',
                        'description' => $section->description ?? null,
                        'students_count' => $studentsCount,
                        'is_active' => $section->is_active ?? false,
                    ];
                });

            \Log::info('Found ' . $sections->count() . ' sections');

            return response()->json([
                'success' => true,
                'sections' => $sections
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching sections for class: ' . $e->getMessage());
            \Log::error('Stack trace: ' . $e->getTraceAsString());
            return response()->json([
                'success' => false,
                'message' => 'Error loading sections: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get students for a class (AJAX)
     */
    public function getStudents($classId)
    {
        try {
            \Log::info('Fetching students for class ID: ' . $classId);

            $class = ClassModel::findOrFail($classId);
            \Log::info('Found class: ' . $class->name);

            // Get students through enrollments to avoid relationship issues
            $enrollments = StudentEnrollment::where('class_id', $class->id)
                ->where('is_active', true)
                ->with(['student.user', 'section'])
                ->get();

            $students = $enrollments->map(function ($enrollment) {
                $student = $enrollment->student;
                return [
                    'id' => $student->id,
                    'name' => $student->user->name ?? 'Unknown Student',
                    'student_id' => $student->student_id ?? 'N/A',
                    'section_name' => $enrollment->section->name ?? 'No section',
                    'is_active' => $student->user->is_active ?? false,
                ];
            });

            \Log::info('Found ' . $students->count() . ' students');

            return response()->json([
                'success' => true,
                'students' => $students
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching students for class: ' . $e->getMessage());
            \Log::error('Stack trace: ' . $e->getTraceAsString());
            return response()->json([
                'success' => false,
                'message' => 'Error loading students: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get subjects for a class (AJAX)
     */
    public function getSubjects($classId)
    {
        try {
            \Log::info('Fetching subjects for class ID: ' . $classId);

            $class = ClassModel::findOrFail($classId);
            \Log::info('Found class: ' . $class->name);

            $subjects = $class->subjects()
                ->orderBy('name')
                ->get()
                ->map(function ($subject) {
                    return [
                        'id' => $subject->id,
                        'name' => $subject->name ?? 'Unknown Subject',
                        'subject_code' => $subject->subject_code ?? 'N/A',
                        'credits' => $subject->credits ?? 0,
                        'category' => $subject->category ?? 'Not specified',
                        'is_mandatory' => $subject->pivot->is_mandatory ?? false,
                        'hours_per_week' => $subject->pivot->hours_per_week ?? 0,
                    ];
                });

            \Log::info('Found ' . $subjects->count() . ' subjects');

            return response()->json([
                'success' => true,
                'subjects' => $subjects
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching subjects for class: ' . $e->getMessage());
            \Log::error('Stack trace: ' . $e->getTraceAsString());
            return response()->json([
                'success' => false,
                'message' => 'Error loading subjects: ' . $e->getMessage()
            ], 500);
        }
    }
}
