<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Page Header -->
    <?php if (isset($component)) { $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-header','data' => ['title' => 'Grade Category Details','description' => 'View grade category information and usage','backRoute' => route('admin.grading.grade-categories.index'),'backLabel' => 'Back to Grade Categories']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Grade Category Details','description' => 'View grade category information and usage','back-route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.grading.grade-categories.index')),'back-label' => 'Back to Grade Categories']); ?>

        <div class="flex items-center space-x-3">
            <a href="<?php echo e(route('admin.grading.grade-categories.edit', $gradeCategory)); ?>" class="btn-secondary" style="cursor: pointer;">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
                Edit Grade Category
            </a>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $attributes = $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $component = $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>

    <!-- Grade Category Information -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gradient-to-r from-purple-500 to-purple-600">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-white"><?php echo e($gradeCategory->name); ?></h1>
                    <p class="text-purple-100"><?php echo e($gradeCategory->code); ?></p>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 rounded-full" style="background-color: <?php echo e($gradeCategory->color); ?>"></div>
                    <span class="badge <?php echo e($gradeCategory->is_active ? 'badge-green' : 'badge-red'); ?>">
                        <?php echo e($gradeCategory->is_active ? 'Active' : 'Inactive'); ?>

                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Category Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Category Information</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Name</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($gradeCategory->name); ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Code</label>
                        <p class="mt-1">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium text-white" style="background-color: <?php echo e($gradeCategory->color); ?>">
                                <?php echo e($gradeCategory->code); ?>

                            </span>
                        </p>
                    </div>

                    <?php if($gradeCategory->description): ?>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Description</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($gradeCategory->description); ?></p>
                        </div>
                    <?php endif; ?>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Weight Percentage</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e(number_format($gradeCategory->weight_percentage, 1)); ?>%</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Sort Order</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($gradeCategory->sort_order); ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Created</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($gradeCategory->created_at->format('M d, Y g:i A')); ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Last Updated</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($gradeCategory->updated_at->format('M d, Y g:i A')); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Usage Statistics -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Usage Statistics</h3>

                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Exams:</span>
                        <span class="badge badge-blue"><?php echo e($usage['exams']); ?></span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Assignments:</span>
                        <span class="badge badge-indigo"><?php echo e($usage['assignments']); ?></span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Grades:</span>
                        <span class="badge badge-green"><?php echo e($usage['grades']); ?></span>
                    </div>
                    <hr class="border-gray-200">
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-gray-900">Total Usage:</span>
                        <span class="badge badge-gray"><?php echo e($usage['exams'] + $usage['assignments'] + $usage['grades']); ?></span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>

                <div class="space-y-3">
                    <form method="POST" action="<?php echo e(route('admin.grading.grade-categories.toggle-status', $gradeCategory)); ?>" class="w-full toggle-category-status-form">
                        <?php echo csrf_field(); ?>
                        <button type="button"
                                style="cursor: pointer;"
                                class="w-full <?php echo e($gradeCategory->is_active ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-green-600 hover:bg-green-700'); ?> text-white px-4 py-2 rounded-lg transition-colors text-center toggle-category-status-btn"
                                data-category-id="<?php echo e($gradeCategory->id); ?>"
                                data-category-name="<?php echo e($gradeCategory->name); ?>"
                                data-is-active="<?php echo e($gradeCategory->is_active ? 'true' : 'false'); ?>">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <?php if($gradeCategory->is_active): ?>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                <?php else: ?>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2 4H7a2 2 0 01-2-2V8a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2z"></path>
                                <?php endif; ?>
                            </svg>
                            <?php echo e($gradeCategory->is_active ? 'Deactivate' : 'Activate'); ?>

                        </button>
                    </form>

                    <?php if($usage['exams'] + $usage['assignments'] + $usage['grades'] == 0): ?>
                        <form method="POST" action="<?php echo e(route('admin.grading.grade-categories.destroy', $gradeCategory)); ?>" class="w-full delete-category-form">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="button"
                                    style="cursor: pointer;"
                                    class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors text-center delete-category-btn"
                                    data-category-id="<?php echo e($gradeCategory->id); ?>"
                                    data-category-name="<?php echo e($gradeCategory->name); ?>">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                Delete Category
                            </button>
                        </form>
                    <?php else: ?>
                        <button type="button"
                                class="w-full bg-gray-400 text-white px-4 py-2 rounded-lg text-center cursor-not-allowed"
                                disabled
                                title="Cannot delete category that is currently in use">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete (In Use)
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Handle toggle category status with custom modal confirmation
document.addEventListener('DOMContentLoaded', function() {
    const toggleButtons = document.querySelectorAll('.toggle-category-status-btn');
    const deleteButtons = document.querySelectorAll('.delete-category-btn');

    // Toggle status functionality
    toggleButtons.forEach(button => {
        button.addEventListener('click', async function(e) {
            e.preventDefault();

            const categoryId = this.getAttribute('data-category-id');
            const categoryName = this.getAttribute('data-category-name');
            const isActive = this.getAttribute('data-is-active') === 'true';

            const action = isActive ? 'deactivate' : 'activate';
            const actionText = action.charAt(0).toUpperCase() + action.slice(1);

            const confirmed = await confirmModal({
                title: `${actionText} Grade Category`,
                message: `Are you sure you want to ${action} the grade category "${categoryName}"?`,
                confirmText: actionText,
                cancelText: 'Cancel',
                type: isActive ? 'warning' : 'success'
            });

            if (confirmed) {
                // Submit the form
                const form = this.closest('.toggle-category-status-form');
                form.submit();
            }
        });
    });

    // Delete functionality
    deleteButtons.forEach(button => {
        button.addEventListener('click', async function(e) {
            e.preventDefault();

            const categoryId = this.getAttribute('data-category-id');
            const categoryName = this.getAttribute('data-category-name');

            const confirmed = await confirmModal({
                title: 'Delete Grade Category',
                message: `Are you sure you want to delete the grade category "${categoryName}"? This action cannot be undone.`,
                confirmText: 'Delete',
                cancelText: 'Cancel',
                type: 'danger'
            });

            if (confirmed) {
                // Submit the form
                const form = this.closest('.delete-category-form');
                form.submit();
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\school-management-system\resources\views/admin/grade-categories/show.blade.php ENDPATH**/ ?>